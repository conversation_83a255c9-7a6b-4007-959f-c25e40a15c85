<script setup>
import { ref, onMounted, h } from 'vue'
import { NButton, NTag, NSelect, NInput, NDatePicker, useMessage } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import TaskCompleteModal from './components/TaskCompleteModal.vue'

import { formatDate, renderIcon } from '@/utils'
import api from '@/api'

defineOptions({ name: '任务工作台' })

const message = useMessage()
const $table = ref(null)
const queryItems = ref({
  taskName: '',
  taskType: null,
  assignee: '',
  status: null,
  startDate: null,
  endDate: null,
})

// 任务完成弹窗相关
const taskCompleteVisible = ref(false)
const currentTask = ref({})

// 下拉选项数据
const taskTypeOptions = ref([
  { label: '车型+OE', value: 'vehicle_oe' },
  { label: '新增产品', value: 'new_product' },
  { label: '询报价', value: 'inquiry_quote' },
  { label: '新产品开发', value: 'new_product_dev' },
])

const statusOptions = ref([
  { label: '待处理', value: 'pending' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
])

// 表格列定义
const columns = [
  {
    title: '任务名称',
    key: 'taskName',
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '任务类型',
    key: 'taskType',
    width: 120,
    render: (row) => {
      const typeMap = {
        vehicle_oe: { type: 'info', text: '车型+OE' },
        new_product: { type: 'success', text: '新增产品' },
        inquiry_quote: { type: 'warning', text: '询报价' },
        new_product_dev: { type: 'error', text: '新产品开发' },
      }
      const config = typeMap[row.taskType] || { type: 'default', text: row.taskType }
      return h(NTag, { type: config.type }, { default: () => config.text })
    },
  },
  {
    title: '负责人',
    key: 'assignee',
    width: 100,
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        pending: { type: 'default', text: '待处理' },
        in_progress: { type: 'info', text: '进行中' },
        completed: { type: 'success', text: '已完成' },
        cancelled: { type: 'error', text: '已取消' },
      }
      const config = statusMap[row.status] || { type: 'default', text: row.status }
      return h(NTag, { type: config.type }, { default: () => config.text })
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 160,
    render: (row) => formatDate(row.createTime),
  },
  {
    title: '截止日期',
    key: 'dueDate',
    width: 160,
    render: (row) => formatDate(row.dueDate),
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render: (row) => {
      return h('div', { class: 'flex gap-2' }, [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleViewDetail(row),
          },
          { default: () => '任务详情', icon: renderIcon('material-symbols:visibility') }
        ),
        row.status !== 'completed' && row.status !== 'cancelled'
          ? h(
              NButton,
              {
                size: 'small',
                type: 'success',
                onClick: () => handleCompleteTask(row),
              },
              { default: () => '完成任务', icon: renderIcon('material-symbols:check-circle') }
            )
          : null,
      ])
    },
  },
]

// 获取任务列表数据
const getTaskList = async (params = {}) => {
  try {
    // 调用实际的API接口
    const response = await api.getTaskList(params)
    return response
  } catch (error) {
    console.error('获取任务列表失败:', error)

    // 如果API调用失败，返回模拟数据作为备用
    const mockData = {
      data: [
        {
          id: 1,
          taskName: '奔驰C级车型信息录入',
          taskType: 'vehicle_oe',
          assignee: '张三',
          status: 'pending',
          createTime: '2024-01-15 10:30:00',
          dueDate: '2024-01-20 18:00:00',
          description: '录入奔驰C级2024款车型基础信息和OE配件信息',
        },
        {
          id: 2,
          taskName: '新增刹车片产品',
          taskType: 'new_product',
          assignee: '李四',
          status: 'in_progress',
          createTime: '2024-01-14 14:20:00',
          dueDate: '2024-01-18 17:00:00',
          description: '新增博世品牌刹车片产品信息',
        },
        {
          id: 3,
          taskName: '客户询价处理',
          taskType: 'inquiry_quote',
          assignee: '王五',
          status: 'completed',
          createTime: '2024-01-13 09:15:00',
          dueDate: '2024-01-16 16:00:00',
          description: '处理客户关于机油滤清器的询价请求',
        },
      ],
      total: 3,
    }

    return mockData
  }
}

// 查看任务详情
const handleViewDetail = (row) => {
  message.info(`查看任务详情: ${row.taskName}`)
  // 这里可以打开任务详情弹窗或跳转到详情页面
}

// 完成任务
const handleCompleteTask = (row) => {
  currentTask.value = row
  taskCompleteVisible.value = true
}

// 任务完成回调
const handleTaskCompleted = () => {
  taskCompleteVisible.value = false
  message.success('任务完成成功')
  $table.value?.handleSearch()
}

onMounted(() => {
  $table.value?.handleSearch()
})
</script>

<template>
  <CommonPage title="任务工作台">
    <template #action>
      <NButton type="primary" @click="$table?.handleSearch()">
        <template #icon>
          <TheIcon icon="material-symbols:refresh" :size="18" />
        </template>
        刷新
      </NButton>
    </template>

    <!-- 任务列表表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getTaskList"
      :scroll-x="1200"
    >
      <template #queryBar>
        <QueryBarItem label="任务名称" :label-width="80">
          <NInput
            v-model:value="queryItems.taskName"
            clearable
            placeholder="请输入任务名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="任务类型" :label-width="80">
          <NSelect
            v-model:value="queryItems.taskType"
            :options="taskTypeOptions"
            clearable
            placeholder="请选择任务类型"
            @change="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="负责人" :label-width="80">
          <NInput
            v-model:value="queryItems.assignee"
            clearable
            placeholder="请输入负责人"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="状态" :label-width="80">
          <NSelect
            v-model:value="queryItems.status"
            :options="statusOptions"
            clearable
            placeholder="请选择状态"
            @change="$table?.handleSearch()"
          />
        </QueryBarItem>

        <QueryBarItem label="截止日期" :label-width="80">
          <NDatePicker
            v-model:value="queryItems.startDate"
            type="date"
            clearable
            placeholder="开始日期"
            style="width: 140px; margin-right: 8px"
          />
          <NDatePicker
            v-model:value="queryItems.endDate"
            type="date"
            clearable
            placeholder="结束日期"
            style="width: 140px"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 任务完成弹窗 -->
    <TaskCompleteModal
      v-model:visible="taskCompleteVisible"
      :task="currentTask"
      @completed="handleTaskCompleted"
    />
  </CommonPage>
</template>

<style scoped>
.flex {
  display: flex;
}

.gap-2 {
  gap: 8px;
}
</style>
